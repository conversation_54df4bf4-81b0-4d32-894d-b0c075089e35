@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">{{ $title }}</h4>
                    <a href="{{ route('asset-management.asset-loan.index') }}" class="btn btn-sm btn-outline-secondary-modern">
                        <i class="fas fa-arrow-left"></i> Kembali ke Daftar
                    </a>
                </div>
                <div class="card-body">
                    <form id="assetLoanForm" method="POST" action="{{ route('asset-management.asset-loan.store') }}">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Ruangan Asal <span class="text-danger">*</span></label>
                                    <select name="origin_room_id" id="originRoom" class="form-control select2" required>
                                        <option value="">Pilih Ruangan Asal</option>
                                        @foreach($rooms as $room)
                                        <option value="{{ $room->id }}">{{ $room->room_code }} - {{ $room->room_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Ruangan Tujuan <span class="text-danger">*</span></label>
                                    <select name="target_room_id" id="targetRoom" class="form-control select2" required>
                                        <option value="">Pilih Ruangan Tujuan</option>
                                        @foreach($rooms as $room)
                                        <option value="{{ $room->id }}">{{ $room->room_code }} - {{ $room->room_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Penanggung Jawab Ruangan <span class="text-danger">*</span></label>
                                    <select name="pic_room_id" id="picRoom" class="form-control select2" required>
                                        <option value="">Pilih Penanggung Jawab Ruangan</option>
                                        @foreach($employees as $employee)
                                        <option value="{{ $employee->id }}">{{ $employee->employee_name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Tanggal Peminjaman <span class="text-danger">*</span></label>
                                    <input type="date" name="loaned_at" id="loanedAt" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">Alasan <span class="text-danger">*</span></label>
                                    <textarea name="reason" id="reason" class="form-control" rows="3" required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Item Peminjaman</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered" id="loanItemsTable">
                                                <thead>
                                                    <tr>
                                                        <th width="5%">#</th>
                                                        <th>Aset</th>
                                                        <th>Tanggal Pengembalian yang Diharapkan</th>
                                                        <th width="10%">Aksi</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="loanItemsContainer">
                                                    <!-- Loan items will be added here dynamically -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <button type="button" id="addLoanItem" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus"></i> Tambah Item
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">Buat Peminjaman Aset</button>
                                <a href="{{ route('asset-management.asset-loan.index') }}" class="btn btn-outline-secondary">Batal</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Asset Selection Modal -->
<div class="modal fade" id="assetModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Pilih Aset</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Pilih Ruangan untuk Memfilter Aset</label>
                    <select id="modalRoomFilter" class="form-control select2">
                        <option value="">Semua Ruangan</option>
                        @foreach($rooms as $room)
                        <option value="{{ $room->id }}">{{ $room->room_code }} - {{ $room->room_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered" id="assetsTable">
                        <thead>
                            <tr>
                                <th>Nama Aset</th>
                                <th>Kode Registrasi</th>
                                <th>Merk</th>
                                <th>Tipe</th>
                                <th width="10%">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($assets as $asset)
                            <tr>
                                <td>{{ $asset->name }}</td>
                                <td>{{ $asset->register_code }}</td>
                                <td>{{ $asset->merk }}</td>
                                <td>{{ $asset->type }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-asset"
                                        data-id="{{ $asset->id }}"
                                        data-name="{{ $asset->name }}"
                                        data-register-code="{{ $asset->register_code }}"
                                        data-merk="{{ $asset->merk }}"
                                        data-type="{{ $asset->type }}">Pilih</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>

<!-- Define global variables for the external JavaScript file -->
<script>
    window.allAssetsData = @json($assets);
    window.assetLoanGetAssetsByRoomRoute = '{{ route("asset-management.asset-loan.get-assets-by-room") }}';
</script>

<!-- Include the external JavaScript file -->
<script src="{{ asset('js/app/asset-management/asset-loan-create.js') }}"></script>
@endpush