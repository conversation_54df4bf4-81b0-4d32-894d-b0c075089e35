@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in asset loan table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <!-- Add action buttons here when needed -->
            <a href="{{ route('asset-management.asset-loan.create') }}" class="btn btn-modern-rounded btn-primary">
                <i class="fas fa-plus"></i> Buat Peminjaman Asset
            </a>
            <div class="datatable-filter-icon btn-modern-rounded" data-bs-toggle="modal" data-bs-target="#filterDrawer">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <!-- Active filter indicators -->
    <div id="activeFiltersContainer" class="mb-3 d-flex flex-wrap gap-2" style="display: none;"></div>

    <div class="table-responsive">
        <table class="table table-sm datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>No. Pinjaman</th>
                    <th>Ruangan Asal</th>
                    <th>Ruangan Tujuan</th>
                    <th>PIC Tujuan</th>
                    <th>Tgl. Pinjam</th>
                    <th>Status</th>
                    <th>Alasan</th>
                    <th style="width: 80px;">Actions</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Status Pengembalian Filter -->
                <div class="form-group mb-3">
                    <label class="form-label">Status Pengembalian</label>
                    <select name="status" id="statusFilter" class="form-select form-control">
                        <option value="">Semua Status</option>
                        @foreach(App\Models\AssetLoan::getStatuses() as $status)
                        <option value="{{ $status }}">{{ ucfirst(str_replace('_', ' ', $status)) }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Ruangan Asal Filter -->
                <div class="form-group mb-3">
                    <label class="form-label">Ruangan Asal</label>
                    <select name="origin_room_id" id="originRoomFilter" class="form-control select2-filter" data-placeholder="Pilih Ruangan Asal">
                        <option value=""></option>
                    </select>
                </div>

                <!-- Ruangan Target Filter -->
                <div class="form-group mb-3">
                    <label class="form-label">Ruangan Target</label>
                    <select name="target_room_id" id="targetRoomFilter" class="form-control select2-filter" data-placeholder="Pilih Ruangan Target">
                        <option value=""></option>
                    </select>
                </div>

                <!-- Drawer actions -->
                <div class="d-flex gap-2 pt-2">
                  <button type="button" id="filterSubmitBtn" class="btn btn-primary">Submit Filter</button>
                  <button type="button" id="filterResetBtn" class="btn btn-outline-secondary">Reset Filter</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<!-- Asset Loan JavaScript file will be added here when created -->
<script src="{{ asset('/js/app/asset-management/asset-loan.js') }}"></script>
@endpush