@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">Asset Loan Detail</h4>
                    <div>
                        <a href="{{ route('asset-management.asset-loan.index') }}" class="btn btn-sm btn-outline-secondary-modern me-2">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <a href="{{ route('asset-management.asset-loan.edit', $assetLoan) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Loan Number</label>
                                <input type="text" class="form-control" value="{{ $assetLoan->loan_no }}" readonly>
                            </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Status</label>
                                <input type="text" class="form-control" 
                                    value="{{ ucfirst($assetLoan->status) }}" 
                                    readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Origin Room</label>
                                <input type="text" class="form-control" 
                                    value="{{ $assetLoan->originRoom->room_code ?? '-' }} - {{ $assetLoan->originRoom->room_name ?? '-' }}" 
                                    readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Target Room</label>
                                <input type="text" class="form-control" 
                                    value="{{ $assetLoan->targetRoom->room_code ?? '-' }} - {{ $assetLoan->targetRoom->room_name ?? '-' }}" 
                                    readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">PIC Room</label>
                                <input type="text" class="form-control" 
                                    value="{{ $assetLoan->picRoom->employee_name ?? '-' }}" 
                                    readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Loaned Date</label>
                                <input type="text" class="form-control" 
                                    value="{{ $assetLoan->formatted_loaned_at }}" 
                                    readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Returned Date</label>
                                <input type="text" class="form-control" 
                                    value="{{ $assetLoan->formatted_returned_at ?: '-' }}" 
                                    readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label">Reason</label>
                                <textarea class="form-control" rows="3" readonly>{{ $assetLoan->reason }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Loan Items</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th width="5%">#</th>
                                                    <th>Asset Name</th>
                                                    <th>Register Code</th>
                                                    <th>Expected Return Date</th>
                                                    <th>Returned Date</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($assetLoan->loanItems as $index => $item)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $item->asset->name ?? '-' }}</td>
                                                    <td>{{ $item->asset->register_code ?? '-' }}</td>
                                                    <td>{{ $item->formattedExpectedReturnAt ?? '-' }}</td>
                                                    <td>{{ $item->formattedReturnedAt ?? '-' }}</td>
                                                    <td>
                                                        <span class="badge {{ $item->returned_at ? 'bg-success' : 'bg-warning' }}">
                                                            {{ $item->getStatusFromReturnedAt() }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">No loan items found</td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
@endpush