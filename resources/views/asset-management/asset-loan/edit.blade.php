@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">{{ $title }}</h4>
                    <a href="{{ route('asset-management.asset-loan.index') }}" class="btn btn-sm btn-outline-secondary-modern">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <div class="card-body">
                    <form id="assetLoanForm" method="POST" action="{{ route('asset-management.asset-loan.update', $assetLoan) }}">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Origin Room <span class="text-danger">*</span></label>
                                    <select name="origin_room_id" id="originRoom" class="form-control select2" required>
                                        <option value="">Select Origin Room</option>
                                        @foreach($rooms as $room)
                                        <option value="{{ $room->id }}" {{ $assetLoan->origin_room_id == $room->id ? 'selected' : '' }}>
                                            {{ $room->room_code }} - {{ $room->room_name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Target Room <span class="text-danger">*</span></label>
                                    <select name="target_room_id" id="targetRoom" class="form-control select2" required>
                                        <option value="">Select Target Room</option>
                                        @foreach($rooms as $room)
                                        <option value="{{ $room->id }}" {{ $assetLoan->target_room_id == $room->id ? 'selected' : '' }}>
                                            {{ $room->room_code }} - {{ $room->room_name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">PIC Room <span class="text-danger">*</span></label>
                                    <select name="pic_room_id" id="picRoom" class="form-control select2" required>
                                        <option value="">Select PIC Room</option>
                                        @foreach($employees as $employee)
                                        <option value="{{ $employee->id }}" {{ $assetLoan->pic_room_id == $employee->id ? 'selected' : '' }}>
                                            {{ $employee->employee_name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Loaned Date <span class="text-danger">*</span></label>
                                    <input type="date" name="loaned_at" id="loanedAt" class="form-control"
                                        value="{{ $assetLoan->loaned_at->format('Y-m-d') }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">Reason <span class="text-danger">*</span></label>
                                    <textarea name="reason" id="reason" class="form-control" rows="3" required>{{ $assetLoan->reason }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">Loan Items</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-bordered" id="loanItemsTable">
                                                <thead>
                                                    <tr>
                                                        <th width="5%">#</th>
                                                        <th>Asset</th>
                                                        <th>Expected Return Date</th>
                                                        <th width="10%">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="loanItemsContainer">
                                                    @foreach($assetLoan->loanItems as $item)
                                                    <tr data-item-id="{{ $item->id }}">
                                                        <td>{{ $loop->iteration }}</td>
                                                        <td>
                                                            <input type="hidden" name="loan_items[{{ $loop->index }}][id]" value="{{ $item->id }}">
                                                            <input type="hidden" name="loan_items[{{ $loop->index }}][asset_id]" value="{{ $item->asset_id }}" class="asset-id">
                                                            <strong>{{ $item->asset->name ?? '-' }}</strong><br>
                                                            <small>Code: {{ $item->asset->register_code ?? '-' }} | Merk: {{ $item->asset->merk ?? '-' }} | Type: {{ $item->asset->type ?? '-' }}</small>
                                                        </td>
                                                        <td>
                                                            <input type="date" name="loan_items[{{ $loop->index }}][expected_return_at]"
                                                                class="form-control expected-return-date"
                                                                value="{{ $item->expected_return_at ? $item->expected_return_at->format('Y-m-d') : '' }}"
                                                                required>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-danger remove-item"
                                                                data-item-id="{{ $item->id }}">Remove</button>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <button type="button" id="addLoanItem" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus"></i> Add Item
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">Update Asset Loan</button>
                                <a href="{{ route('asset-management.asset-loan.index') }}" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Asset Selection Modal -->
<div class="modal fade" id="assetModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Asset</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Select Room to Filter Assets</label>
                    <select id="modalRoomFilter" class="form-control select2">
                        <option value="">All Rooms</option>
                        @foreach($rooms as $room)
                        <option value="{{ $room->id }}">{{ $room->room_code }} - {{ $room->room_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered" id="assetsTable">
                        <thead>
                            <tr>
                                <th>Asset Name</th>
                                <th>Register Code</th>
                                <th>Merk</th>
                                <th>Type</th>
                                <th width="10%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($assets as $asset)
                            <tr>
                                <td>{{ $asset->name }}</td>
                                <td>{{ $asset->register_code }}</td>
                                <td>{{ $asset->merk }}</td>
                                <td>{{ $asset->type }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary select-asset" 
                                        data-id="{{ $asset->id }}" 
                                        data-name="{{ $asset->name }}"
                                        data-register-code="{{ $asset->register_code }}"
                                        data-merk="{{ $asset->merk }}"
                                        data-type="{{ $asset->type }}">Select</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2();

        // Initialize date picker
        $('#loanedAt').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true
        });

        // Add loan item
        $('#addLoanItem').click(function() {
            $('#assetModal').modal('show');
        });

        // Select asset from modal
        $(document).on('click', '.select-asset', function() {
            const assetId = $(this).data('id');
            const assetName = $(this).data('name');
            const registerCode = $(this).data('register-code');
            const merk = $(this).data('merk');
            const type = $(this).data('type');

            // Check if asset is already added
            let exists = false;
            $('.asset-id').each(function() {
                if ($(this).val() == assetId) {
                    exists = true;
                    return false; // break the loop
                }
            });
            
            if (exists) {
                swal('Warning', 'This asset is already added to the loan.', 'warning');
                return;
            }

            // Calculate the next index for new item
            const existingItemCount = $('#loanItemsContainer tr[data-item-id]').length;
            const newItemsCount = $('#loanItemsContainer tr[data-item-new]').length;
            const nextIndex = existingItemCount + newItemsCount;
            
            // Add new row to the table
            const newRow = `
                <tr data-item-new="true">
                    <td>${nextIndex + 1}</td>
                    <td>
                        <input type="hidden" name="loan_items[${nextIndex}][asset_id]" value="${assetId}" class="asset-id">
                        <strong>${assetName}</strong><br>
                        <small>Code: ${registerCode} | Merk: ${merk} | Type: ${type}</small>
                    </td>
                    <td>
                        <input type="date" name="loan_items[${nextIndex}][expected_return_at]"
                            class="form-control expected-return-date"
                            required>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-item"
                            data-item-new="true">Remove</button>
                    </td>
                </tr>
            `;
            
            $('#loanItemsContainer').append(newRow);

            // Initialize date picker for new input
            $(newRow).find('.expected-return-date').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true
            });

            // Close modal
            $('#assetModal').modal('hide');
        });

        // Remove loan item
        $(document).on('click', '.remove-item', function() {
            if (confirm('Are you sure you want to remove this item?')) {
                const row = $(this).closest('tr');
                if (row.data('item-id')) {
                    // This is an existing item, mark it for deletion
                    const itemId = row.data('item-id');
                    // Add a hidden input to mark this item for deletion
                    const deleteInput = `<input type="hidden" name="delete_items[]" value="${itemId}">`;
                    $('#assetLoanForm').append(deleteInput);
                    row.hide(); // Hide the row instead of removing it
                } else {
                    // This is a new item, remove the row completely
                    row.remove();
                    
                    // Update row numbers
                    $('#loanItemsContainer tr:visible').each(function(index) {
                        $(this).find('td:first').text(index + 1);
                    });
                }
            }
        });

        // Filter assets by room
        $('#modalRoomFilter').change(function() {
            const roomId = $(this).val();
            if (roomId) {
                $.get('{{ route("asset-management.asset-loan.get-assets-by-room") }}', { room_id: roomId }, function(data) {
                    updateAssetsTable(data);
                });
            } else {
                // Reset to all assets
                updateAssetsTable(@json($assets));
            }
        });

        // Initialize date pickers for existing items
        $('.expected-return-date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true
        });

        // Form submission
        $('#assetLoanForm').submit(function(e) {
            e.preventDefault();

            // Prepare the form data with proper indexing
            const formData = new FormData(this);
            
            // Get all visible rows and re-index them
            const visibleRows = $('#loanItemsContainer tr:visible');
            visibleRows.each(function(index) {
                const row = $(this);
                if (row.data('item-id')) {
                    // Existing item
                    const itemId = row.data('item-id');
                    const assetId = row.find('.asset-id').val();
                    const expectedReturnDate = row.find('.expected-return-date').val();
                    
                    // Update the form inputs with proper index
                    row.find('input[name*="[id]"]').attr('name', `loan_items[${index}][id]`);
                    row.find('input[name*="[asset_id]"]').attr('name', `loan_items[${index}][asset_id]`).val(assetId);
                    row.find('input[name*="[expected_return_at]"]').attr('name', `loan_items[${index}][expected_return_at]`).val(expectedReturnDate);
                } else if (row.data('item-new')) {
                    // New item
                    const assetId = row.find('.asset-id').val();
                    const expectedReturnDate = row.find('.expected-return-date').val();
                    
                    // Update the form inputs with proper index
                    row.find('input[name*="[asset_id]"]').attr('name', `loan_items[${index}][asset_id]`).val(assetId);
                    row.find('input[name*="[expected_return_at]"]').attr('name', `loan_items[${index}][expected_return_at]`).val(expectedReturnDate);
                }
            });

            // Submit the form
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: new FormData(this),
                processData: false,
                contentType: false,
                headers: {
                    'X-HTTP-Method-Override': 'PUT'
                },
                success: function(response) {
                    if (response.success) {
                        swal('Success', response.message, 'success');
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1500);
                    } else {
                        swal('Error', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    swal('Error', response.message || 'An error occurred', 'error');
                }
            });
        });
    });

    function updateAssetsTable(assets) {
        let html = '';
        assets.forEach(asset => {
            html += `
                <tr>
                    <td>${asset.name}</td>
                    <td>${asset.register_code}</td>
                    <td>${asset.merk}</td>
                    <td>${asset.type}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-primary select-asset"
                            data-id="${asset.id}"
                            data-name="${asset.name}"
                            data-register-code="${asset.register_code}"
                            data-merk="${asset.merk}"
                            data-type="${asset.type}">Select</button>
                    </td>
                </tr>
            `;
        });

        $('#assetsTable tbody').html(html);
    }
</script>
@endpush