<?php

namespace App\Models;

use App\Enums\AssetLoanStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AssetLoan extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'asset_loans';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'origin_room_id',
        'target_room_id',
        'pic_room_id',
        'created_by',
        'updated_by',
        'loaned_at',
        'returned_at',
        'status',
        'reason',
        'loan_no',
        'created_by_name',
        'updated_by_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'loaned_at' => 'datetime',
        'returned_at' => 'datetime',
        'status' => AssetLoanStatusEnum::class,
    ];

    /**
     * Status constants
     */
    public const STATUS_RETURNED = 'returned';
    public const STATUS_BORROWED = 'borrowed';
    public const STATUS_NOT_RETURNED = 'not_returned';
    public const STATUS_PARTIALLY_RETURNED = 'partially_returned';
    public const STATUS_FULLY_RETURNED = 'fully_returned';

    /**
     * Get all status options as an array
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_BORROWED,
            self::STATUS_RETURNED,
            self::STATUS_NOT_RETURNED,
            self::STATUS_PARTIALLY_RETURNED,
            self::STATUS_FULLY_RETURNED,
        ];
    }

    /**
     * Get the origin room that owns the loan.
     */
    public function originRoom(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'origin_room_id');
    }

    /**
     * Get the target room that owns the loan.
     */
    public function targetRoom(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'target_room_id');
    }

    /**
     * Get the PIC room employee that owns the loan.
     */
    public function picRoom(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'pic_room_id');
    }

    /**
     * Get the user who created the loan.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the loan.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the loaned date in formatted way.
     */
    public function getFormattedLoanedAtAttribute()
    {
        return $this->loaned_at ? $this->loaned_at->format('Y-m-d') : null;
    }

    /**
     * Get the returned date in formatted way.
     */
    public function getFormattedReturnedAtAttribute()
    {
        return $this->returned_at ? $this->returned_at->format('Y-m-d') : null;
    }

    /**
     * Get the status attribute based on returned_at date.
     * If returned_at is null/empty, status is 'borrowed', otherwise 'returned'.
     */
     public function getStatusAttribute(): string
     {
         return $this->returned_at ? self::STATUS_RETURNED : self::STATUS_BORROWED;
     }

     /**
      * Get the loan items for this loan.
      */
     public function loanItems(): HasMany
     {
         return $this->hasMany(AssetLoanItem::class, 'loan_id');
     }

     /**
      * Determine if all items in the loan have been returned.
      */
     public function getIsFullyReturnedAttribute(): bool
     {
         return $this->loanItems->every(function ($item) {
             return $item->returned_at !== null;
         });
     }
 }