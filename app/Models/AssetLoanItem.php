<?php

namespace App\Models;

use App\Enums\AssetLoanItemStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetLoanItem extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'asset_loan_items';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'loan_id',
        'asset_id',
        'expected_return_at',
        'returned_at',
        'status',
        'return_pic_id',
        'created_by',
        'updated_by',
        'created_by_name',
        'updated_by_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expected_return_at' => 'datetime',
        'returned_at' => 'datetime',
        'status' => AssetLoanItemStatusEnum::class,
    ];

    /**
     * Status constants
     */
    public const STATUS_RETURNED = 'returned';
    public const STATUS_BORROWED = 'borrowed';

    /**
     * Get all status options as an array
     *
     * @return array
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_BORROWED,
            self::STATUS_RETURNED,
        ];
    }

    /**
     * Get the loan that owns the loan item.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(AssetLoan::class, 'loan_id');
    }

    /**
     * Get the asset that belongs to the loan item.
     */
    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    /**
     * Get the employee who handled the return of this loan item.
     */
    public function returnPic(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'return_pic_id');
    }

    /**
     * Get the user who created the loan item.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the loan item.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the expected return date in formatted way.
     */
    public function getFormattedExpectedReturnAtAttribute()
    {
        return $this->expected_return_at ? $this->expected_return_at->format('Y-m-d') : null;
    }

    /**
     * Get the returned date in formatted way.
     */
    public function getFormattedReturnedAtAttribute()
    {
        return $this->returned_at ? $this->returned_at->format('Y-m-d') : null;
    }

    /**
     * Get the status based on returned_at date.
     * If returned_at is null/empty, status is 'borrowed', otherwise 'returned'.
     */
    public function getStatusFromReturnedAt(): string
    {
        return $this->returned_at ? self::STATUS_RETURNED : self::STATUS_BORROWED;
    }
}