<?php

namespace App\DataTables;

use App\Models\AssetLoan;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AssetLoanDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addIndexColumn()
            ->addColumn('formatted_loaned_at', function ($row) {
                return $row->formatted_loaned_at;
            })
            ->addColumn('status', function ($row) {
                // Determine status based on loan items
                $allReturned = true;
                $anyBorrowed = false;

                foreach ($row->loanItems as $item) {
                    if ($item->returned_at === null) {
                        $allReturned = false;
                        $anyBorrowed = true;
                    }
                }

                if ($allReturned && $row->loanItems->count() > 0) {
                    $status = 'returned';
                    $badgeClass = 'bg-success';
                } else if ($anyBorrowed) {
                    $status = 'borrowed';
                    $badgeClass = 'bg-warning';
                } else {
                    // No items or all items are returned
                    $status = 'returned';
                    $badgeClass = 'bg-success';
                }

                return '<span class="badge ' . $badgeClass . '">' . ucfirst($status) . '</span>';
            })
            ->addColumn('action', function ($row) {
                return '<a href="javascript:;" class="btn-show" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
            })
            ->rawColumns(['status', 'action'])
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(AssetLoan $model): QueryBuilder
    {
        $query = $model->newQuery()->with(['originRoom', 'targetRoom', 'picRoom', 'loanItems.asset']);

        // Apply status filter
        $status = $this->request()->get('status');
        if ($status !== null && $status !== '') {
            // Filter based on loan items status
            if ($status === 'returned') {
                $query->whereHas('loanItems', function ($q) {
                    $q->whereNotNull('returned_at');
                })->whereDoesntHave('loanItems', function ($q) {
                    $q->whereNull('returned_at');
                });
            } else if ($status === 'borrowed') {
                $query->whereHas('loanItems', function ($q) {
                    $q->whereNull('returned_at');
                });
            }
        }

        // Apply origin room filter
        $originRoomId = $this->request()->get('origin_room_id');
        if ($originRoomId !== null && $originRoomId !== '') {
            $query->where('origin_room_id', $originRoomId);
        }

        // Apply target room filter
        $targetRoomId = $this->request()->get('target_room_id');
        if ($targetRoomId !== null && $targetRoomId !== '') {
            $query->where('target_room_id', $targetRoomId);
        }

        return $query->latest('created_at');
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('datatable')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(1)
            ->selectStyleSingle()
            ->buttons([
                Button::make('excel'),
                Button::make('csv'),
                Button::make('pdf'),
                Button::make('print'),
                Button::make('reset'),
                Button::make('reload'),
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')
                ->title('#')
                ->orderable(false)
                ->searchable(false),
            Column::make('loan_no')
                ->title('No. Pinjaman'),
            Column::make('originRoom.room_name')
                ->title('Ruangan Asal'),
            Column::make('targetRoom.room_name')
                ->title('Ruangan Tujuan'),
            Column::make('picRoom.employee_name')
                ->title('PIC Tujuan'),
            Column::make('formatted_loaned_at')
                ->title('Tgl. Pinjam'),
            Column::computed('status')
                ->title('Status'),
            Column::make('reason')
                ->title('Alasan'),
            Column::computed('action')
                ->exportable(false)
                ->printable(false)
                ->width(60)
                ->addClass('text-center'),
        ];
    }

    /**
     * Apply query filters for searching.
     */
    protected function applyScopes($query): QueryBuilder
    {
        $query = parent::applyScopes($query);

        if ($this->request()->has('search') && $searchValue = $this->request()->get('search')['value']) {
            $query->where(function ($q) use ($searchValue) {
                $q->where('loan_no', 'like', "%{$searchValue}%")
                    ->orWhereHas('originRoom', function ($qr) use ($searchValue) {
                        $qr->where('room_name', 'like', "%{$searchValue}%");
                    })
                    ->orWhereHas('targetRoom', function ($qr) use ($searchValue) {
                        $qr->where('room_name', 'like', "%{$searchValue}%");
                    })
                    ->orWhereHas('picRoom', function ($qr) use ($searchValue) {
                        $qr->where('employee_name', 'like', "%{$searchValue}%");
                    })
                    ->orWhere('reason', 'like', "%{$searchValue}%");
            });
        }

        return $query;
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'AssetLoan_' . date('YmdHis');
    }
}
