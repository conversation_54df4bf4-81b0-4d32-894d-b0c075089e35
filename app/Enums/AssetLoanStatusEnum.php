<?php

namespace App\Enums;

enum AssetLoanStatusEnum: string
{
    case NOT_RETURNED = 'not_returned';
    case PARTIALLY_RETURNED = 'partially_returned';
    case FULLY_RETURNED = 'fully_returned';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function labels(): array
    {
        return [
            self::NOT_RETURNED->value => 'Not Returned',
            self::PARTIALLY_RETURNED->value => 'Partially Returned',
            self::FULLY_RETURNED->value => 'Fully Returned',
        ];
    }
}