<?php

namespace App\Enums;

enum AssetLoanItemStatusEnum: string
{
    case BORROWED = 'borrowed';
    case RETURNED = 'returned';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function labels(): array
    {
        return [
            self::BORROWED->value => 'Borrowed',
            self::RETURNED->value => 'Returned',
        ];
    }
}