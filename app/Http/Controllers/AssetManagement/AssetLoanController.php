<?php

namespace App\Http\Controllers\AssetManagement;

use App\DataTables\AssetLoanDataTable;
use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetLoan;
use App\Models\AssetLoanItem;
use App\Models\Employee;
use App\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AssetLoanController extends Controller
{
    public function index(AssetLoanDataTable $datatable)
    {
        $title = 'Asset Loan';
        $breadcrumbs = ['Manajemen Aset', 'Asset Loan'];

        return $datatable->render('asset-management.asset-loan.index', compact('title', 'breadcrumbs'));
    }

    public function list(AssetLoanDataTable $datatable)
    {
        return $datatable->ajax();
    }

    public function create()
    {
        $title = 'Create Asset Loan';
        $breadcrumbs = ['Manajemen Aset', 'Asset Loan', 'Create'];
        
        $rooms = Room::all();
        $employees = Employee::all();
        $assets = Asset::all();

        return view('asset-management.asset-loan.create', compact('title', 'breadcrumbs', 'rooms', 'employees', 'assets'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'origin_room_id' => 'required|exists:rooms,id',
            'target_room_id' => 'required|exists:rooms,id',
            'pic_room_id' => 'required|exists:employees,id',
            'loaned_at' => 'required|date',
            'reason' => 'required|string|max:255',
            'loan_items' => 'required|array|min:1',
            'loan_items.*.asset_id' => 'required|exists:assets,id',
            'loan_items.*.expected_return_at' => 'required|date',
        ]);

        DB::beginTransaction();
        
        try {
            // Create the loan header
            $loan = new AssetLoan();
            $loan->origin_room_id = $request->origin_room_id;
            $loan->target_room_id = $request->target_room_id;
            $loan->pic_room_id = $request->pic_room_id;
            $loan->loaned_at = $request->loaned_at;
            $loan->reason = $request->reason;
            $loan->loan_no = $this->generateLoanNumber();
            $loan->created_by = auth()->id();
            $loan->created_by_name = auth()->user()->name;
            $loan->save();

            // Create loan items
            foreach ($request->loan_items as $item) {
                $loanItem = new AssetLoanItem();
                $loanItem->loan_id = $loan->id;
                $loanItem->asset_id = $item['asset_id'];
                $loanItem->expected_return_at = $item['expected_return_at'];
                $loanItem->status = 'borrowed';
                $loanItem->created_by = auth()->id();
                $loanItem->created_by_name = auth()->user()->name;
                $loanItem->save();
                
                // Update asset status to borrowed
                $asset = Asset::find($item['asset_id']);
                if ($asset) {
                    $asset->status = 'borrowed';
                    $asset->save();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Asset loan created successfully',
                'redirect' => route('asset-management.asset-loan.index')
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create asset loan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(AssetLoan $assetLoan)
    {
        $title = 'Asset Loan Detail';
        $breadcrumbs = ['Manajemen Aset', 'Asset Loan', 'Detail'];
        
        $assetLoan->load('originRoom', 'targetRoom', 'picRoom', 'loanItems.asset', 'loanItems.returnPic');

        return view('asset-management.asset-loan.show', compact('assetLoan', 'title', 'breadcrumbs'));
    }

    public function edit(AssetLoan $assetLoan)
    {
        $title = 'Edit Asset Loan';
        $breadcrumbs = ['Manajemen Aset', 'Asset Loan', 'Edit'];
        
        $assetLoan->load('originRoom', 'targetRoom', 'picRoom', 'loanItems.asset');
        
        $rooms = Room::all();
        $employees = Employee::all();
        $assets = Asset::all();

        return view('asset-management.asset-loan.edit', compact('assetLoan', 'title', 'breadcrumbs', 'rooms', 'employees', 'assets'));
    }

    public function update(Request $request, AssetLoan $assetLoan)
    {
        $request->validate([
            'origin_room_id' => 'required|exists:rooms,id',
            'target_room_id' => 'required|exists:rooms,id',
            'pic_room_id' => 'required|exists:employees,id',
            'loaned_at' => 'required|date',
            'reason' => 'required|string|max:255',
            'loan_items' => 'required|array',
            'loan_items.*.id' => 'nullable|exists:asset_loan_items,id',
            'loan_items.*.asset_id' => 'required|exists:assets,id',
            'loan_items.*.expected_return_at' => 'required|date',
        ]);

        DB::beginTransaction();
        
        try {
            // Update loan header
            $assetLoan->origin_room_id = $request->origin_room_id;
            $assetLoan->target_room_id = $request->target_room_id;
            $assetLoan->pic_room_id = $request->pic_room_id;
            $assetLoan->loaned_at = $request->loaned_at;
            $assetLoan->reason = $request->reason;
            $assetLoan->updated_by = auth()->id();
            $assetLoan->updated_by_name = auth()->user()->name;
            $assetLoan->save();

            // Get existing loan items
            $existingItems = $assetLoan->loanItems->keyBy('id');
            
            // Process loan items
            $processedItemIds = [];
            foreach ($request->loan_items as $itemData) {
                if (isset($itemData['id']) && $existingItems->has($itemData['id'])) {
                    // Update existing item
                    $loanItem = $existingItems->get($itemData['id']);
                    $loanItem->asset_id = $itemData['asset_id'];
                    $loanItem->expected_return_at = $itemData['expected_return_at'];
                    $loanItem->updated_by = auth()->id();
                    $loanItem->updated_by_name = auth()->user()->name;
                    $loanItem->save();
                    
                    $processedItemIds[] = $loanItem->id;
                } else {
                    // Create new item
                    $loanItem = new AssetLoanItem();
                    $loanItem->loan_id = $assetLoan->id;
                    $loanItem->asset_id = $itemData['asset_id'];
                    $loanItem->expected_return_at = $itemData['expected_return_at'];
                    $loanItem->status = 'borrowed';
                    $loanItem->created_by = auth()->id();
                    $loanItem->created_by_name = auth()->user()->name;
                    $loanItem->save();
                    
                    $processedItemIds[] = $loanItem->id;
                }
            }
            
            // Delete items that are not in the request
            $itemsToDelete = $existingItems->filter(function ($item) use ($processedItemIds) {
                return !in_array($item->id, $processedItemIds);
            });
            
            foreach ($itemsToDelete as $item) {
                $item->delete();
                
                // Reset asset status to available
                $asset = Asset::find($item->asset_id);
                if ($asset) {
                    $asset->status = 'available';
                    $asset->save();
                }
            }

            // Handle items explicitly marked for deletion
            if ($request->has('delete_items')) {
                foreach ($request->delete_items as $itemId) {
                    $item = AssetLoanItem::find($itemId);
                    if ($item && $item->loan_id == $assetLoan->id) {
                        $item->delete();
                        
                        // Reset asset status to available
                        $asset = Asset::find($item->asset_id);
                        if ($asset) {
                            $asset->status = 'available';
                            $asset->save();
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Asset loan updated successfully',
                'redirect' => route('asset-management.asset-loan.index')
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update asset loan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(AssetLoan $assetLoan)
    {
        DB::beginTransaction();
        
        try {
            // Delete all loan items first
            foreach ($assetLoan->loanItems as $loanItem) {
                $loanItem->delete();
                
                // Reset asset status to available
                $asset = Asset::find($loanItem->asset_id);
                if ($asset) {
                    $asset->status = 'available';
                    $asset->save();
                }
            }
            
            // Delete the loan header
            $assetLoan->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Asset loan deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete asset loan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function returnItems(Request $request, AssetLoan $assetLoan)
    {
        $request->validate([
            'item_ids' => 'required|array|min:1',
            'item_ids.*' => 'required|exists:asset_loan_items,id,loan_id,' . $assetLoan->id,
            'returned_at' => 'required|date',
            'return_pic_id' => 'required|exists:employees,id',
        ]);

        DB::beginTransaction();
        
        try {
            $updatedItemsCount = 0;
            
            foreach ($request->item_ids as $itemId) {
                $loanItem = AssetLoanItem::where('id', $itemId)
                    ->where('loan_id', $assetLoan->id)
                    ->whereNull('returned_at') // Only allow returning items that haven't been returned yet
                    ->first();
                
                if ($loanItem) {
                    $loanItem->returned_at = $request->returned_at;
                    $loanItem->return_pic_id = $request->return_pic_id;
                    $loanItem->status = 'returned';
                    $loanItem->updated_by = auth()->id();
                    $loanItem->updated_by_name = auth()->user()->name;
                    $loanItem->save();
                    
                    // Update asset status back to available
                    $asset = Asset::find($loanItem->asset_id);
                    if ($asset) {
                        $asset->status = 'available';
                        $asset->save();
                    }
                    
                    $updatedItemsCount++;
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $updatedItemsCount . ' item(s) returned successfully',
                'data' => [
                    'returned_items_count' => $updatedItemsCount,
                    'loan_id' => $assetLoan->id
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to return items: ' . $e->getMessage()
            ], 500);
        }
    }

    private function generateLoanNumber()
    {
        $prefix = 'AL';
        $date = date('Ym');
        $lastLoan = AssetLoan::where('loan_no', 'LIKE', $prefix . $date . '%')
            ->orderBy('loan_no', 'desc')
            ->first();
            
        $number = 1;
        if ($lastLoan) {
            $lastNumber = intval(substr($lastLoan->loan_no, -4));
            $number = $lastNumber + 1;
        }
        
        return $prefix . $date . sprintf('%04d', $number);
    }

    public function getAssetsByRoom(Request $request)
    {
        $roomId = $request->room_id;
        
        $assets = Asset::where('room_id', $roomId)
            ->where('status', 'available')
            ->get(['id', 'name', 'register_code', 'merk', 'type']);

        return response()->json($assets);
    }
}