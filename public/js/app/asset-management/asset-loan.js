const DEFAULT_FILTER_STATE = {
    status: null,
    origin_room_id: null,
    origin_room_text: null,
    target_room_id: null,
    target_room_text: null
};
let lastAppliedFilterState = { ...DEFAULT_FILTER_STATE };
let pendingFilterState = { ...DEFAULT_FILTER_STATE };

$(document).ready(function() {
    const table = $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/asset-loan-list",
            data: function(d) {
                d.status = lastAppliedFilterState.status;
                d.origin_room_id = lastAppliedFilterState.origin_room_id;
                d.target_room_id = lastAppliedFilterState.target_room_id;
            }
        },
        columns: [
            {
                data: "DT_RowIndex",
                name: "DT_RowIndex",
                orderable: false,
                searchable: false,
            },
            {
                data: "loan_no",
                name: "loan_no",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "originRoom.room_name",
                name: "originRoom.room_name",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "targetRoom.room_name",
                name: "targetRoom.room_name",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "picRoom.employee_name",
                name: "picRoom.employee_name",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "formatted_loaned_at",
                name: "formatted_loaned_at",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "status",
                name: "status",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "reason",
                name: "reason",
                render: function (data) {
                    return data ? data : "-";
                }
            },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    var idMatch = data.match(/data-id="([^"]+)"/);
                    var id = idMatch ? idMatch[1] : '';
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-show" href="javascript:;" data-id="${id}"><i class="fas fa-search me-2"></i>View Detail</a></li>
                                    <li><a class="dropdown-item datatable-dropdown-item btn-return-item" href="javascript:;" data-id="${id}"><i class="fas fa-undo me-2"></i>Return Item</a></li>
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        table.search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Handle status filter change - now only updates pending state
    $("#statusFilter").on('change', function () {
        pendingFilterState.status = this.value || null;
    });

    // Initialize Select2 on #originRoomFilter
    const $originRoom = $('#originRoomFilter').select2({
        dropdownParent: $('#filterDrawer'),
        placeholder: $('#originRoomFilter').data('placeholder') || 'Pilih Ruangan Asal',
        allowClear: true,
        width: '100%',
        minimumInputLength: 0,
        ajax: {
            url: '/dropdown/room',
            dataType: 'json',
            delay: 250,
            data: params => ({ q: params.term, page: params.page || 1 }),
            processResults: function (data) {
                return {
                    results: (data.data || []).map(item => ({
                        id: item.id,
                        text: `${item.room_code} - ${item.room_name} (${item.pic_name || "Tidak Ada PIC"})`
                    })),
                    pagination: { more: data.current_page < data.last_page }
                };
            }
        }
    });

    $originRoom.on('select2:select', function (e) {
        const selected = e.params.data || null;
        pendingFilterState.origin_room_id = selected ? selected.id : null;
        pendingFilterState.origin_room_text = selected ? selected.text : null;
    });
    $originRoom.on('select2:clear select2:unselect', function () {
        pendingFilterState.origin_room_id = null;
        pendingFilterState.origin_room_text = null;
    });

    // Ensure default list loads on open with empty search term
    $originRoom.on('select2:open', function() {
        const $input = $('.select2-container--open .select2-search__field');
        if ($input.val() === '') $input.trigger('keyup');
    });

    // Initialize Select2 on #targetRoomFilter
    const $targetRoom = $('#targetRoomFilter').select2({
        dropdownParent: $('#filterDrawer'),
        placeholder: $('#targetRoomFilter').data('placeholder') || 'Pilih Ruangan Target',
        allowClear: true,
        width: '100%',
        minimumInputLength: 0,
        ajax: {
            url: '/dropdown/room',
            dataType: 'json',
            delay: 250,
            data: params => ({ q: params.term, page: params.page || 1 }),
            processResults: function (data) {
                return {
                    results: (data.data || []).map(item => ({
                        id: item.id,
                        text: `${item.room_code} - ${item.room_name} (${item.pic_name || "Tidak Ada PIC"})`
                    })),
                    pagination: { more: data.current_page < data.last_page }
                };
            }
        }
    });

    $targetRoom.on('select2:select', function (e) {
        const selected = e.params.data || null;
        pendingFilterState.target_room_id = selected ? selected.id : null;
        pendingFilterState.target_room_text = selected ? selected.text : null;
    });
    $targetRoom.on('select2:clear select2:unselect', function () {
        pendingFilterState.target_room_id = null;
        pendingFilterState.target_room_text = null;
    });

    // Ensure default list loads on open with empty search term
    $targetRoom.on('select2:open', function() {
        const $input = $('.select2-container--open .select2-search__field');
        if ($input.val() === '') $input.trigger('keyup');
    });

    // Drawer open/close lifecycle with snapshot
    $('#filterDrawer').on('shown.bs.modal', function () {
        // Take snapshot
        pendingFilterState = { ...lastAppliedFilterState };
        // Sync UI controls to pending
        $('#statusFilter').val(pendingFilterState.status).trigger('change.select2');
        // Set origin room select2 programmatically
        if (pendingFilterState.origin_room_id) {
            const option = new Option(pendingFilterState.origin_room_text, pendingFilterState.origin_room_id, true, true);
            $originRoom.empty().append(option).trigger('change');
        } else {
            $originRoom.val(null).trigger('change');
        }
        // Set target room select2 programmatically
        if (pendingFilterState.target_room_id) {
            const option = new Option(pendingFilterState.target_room_text, pendingFilterState.target_room_id, true, true);
            $targetRoom.empty().append(option).trigger('change');
        } else {
            $targetRoom.val(null).trigger('change');
        }
    });
    $('#filterDrawer').on('hidden.bs.modal', function () {
        // Revert UI to lastAppliedFilterState; no reload
        $('#statusFilter').val(lastAppliedFilterState.status).trigger('change.select2');
        if (lastAppliedFilterState.origin_room_id) {
            const option = new Option(lastAppliedFilterState.origin_room_text, lastAppliedFilterState.origin_room_id, true, true);
            $originRoom.empty().append(option).trigger('change');
        } else {
            $originRoom.val(null).trigger('change');
        }
        if (lastAppliedFilterState.target_room_id) {
            const option = new Option(lastAppliedFilterState.target_room_text, lastAppliedFilterState.target_room_id, true, true);
            $targetRoom.empty().append(option).trigger('change');
        } else {
            $targetRoom.val(null).trigger('change');
        }
    });

    // Submit and Reset buttons
    $('#filterSubmitBtn').on('click', function () {
        lastAppliedFilterState = { ...pendingFilterState };
        table.ajax.reload();
        $('#filterDrawer').modal('hide');
        updateActiveFilterIndicators();
    });

    $('#filterResetBtn').on('click', function () {
        lastAppliedFilterState = { ...DEFAULT_FILTER_STATE };
        pendingFilterState = { ...DEFAULT_FILTER_STATE };
        $('#statusFilter').val(null).trigger('change.select2');
        $originRoom.val(null).trigger('change');
        $targetRoom.val(null).trigger('change');
        table.ajax.reload();
        $('#filterDrawer').modal('hide');
        updateActiveFilterIndicators();
    });

    // Active filter indicators
    function getCurrentSearchTerm() {
        try {
            return table.search();
        } catch (e) {}
        const v = $('#searchInput').val();
        return typeof v === 'string' ? v : '';
    }

    function updateActiveFilterIndicators() {
        const $wrap = $('#activeFiltersContainer');
        if (!$wrap.length) return;
        const badges = [];
        const hasStatus = lastAppliedFilterState.status !== null;
        const hasOriginRoom = !!lastAppliedFilterState.origin_room_id;
        const hasTargetRoom = !!lastAppliedFilterState.target_room_id;
        const term = (getCurrentSearchTerm() || '').trim();

        if (hasStatus) {
            const statusMap = {
                'borrowed': 'Borrowed',
                'returned': 'Returned'
            };
            const statusText = statusMap[lastAppliedFilterState.status] || 'Status: ' + lastAppliedFilterState.status;
            badges.push('<span class="badge bg-primary">' + statusText + '</span>');
        }
        if (hasOriginRoom) {
            badges.push('<span class="badge bg-info">Ruangan Asal: ' + (lastAppliedFilterState.origin_room_text || lastAppliedFilterState.origin_room_id) + '</span>');
        }
        if (hasTargetRoom) {
            badges.push('<span class="badge bg-info">Ruangan Target: ' + (lastAppliedFilterState.target_room_text || lastAppliedFilterState.target_room_id) + '</span>');
        }
        if (term) {
            badges.push('<span class="badge bg-secondary">Cari: \'' + $('<div>').text(term).html() + '\'</span>');
        }
        if (badges.length) {
            $wrap.html(badges.join(' ')).show();
        } else {
            $wrap.empty().hide();
        }
        // Toggle visual cue on filter trigger wrapper if present
        const $filterTrigger = $('[data-bs-target="#filterDrawer"], [href="#filterDrawer"]').first();
        const $triggerWrap = $filterTrigger.length ? $filterTrigger.closest('.btn, .btn-group, .input-group, .card-header, body') : $();
        if ($triggerWrap.length) {
            const hasActive = hasStatus || hasOriginRoom || hasTargetRoom;
            $triggerWrap.toggleClass('has-active-filter', hasActive);
        }
    }

    // Bind updates
    table.on('draw', updateActiveFilterIndicators);
    // Also call once on init
    updateActiveFilterIndicators();

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        table.page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // View detail functionality
    $("#datatable").on("click", ".btn-show", function () {
        const id = $(this).data("id");
        window.location.href = `/manajemen-aset/asset-loan/${id}`;
    });

    // Return item functionality - opens modal to select items to return
    $("#datatable").on("click", ".btn-return-item", function () {
        const loanId = $(this).data("id");
        openReturnItemModal(loanId);
    });

    // Function to open return item modal
    function openReturnItemModal(loanId) {
        // Fetch loan details and items
        $.get(`/manajemen-aset/asset-loan/${loanId}`, function(response) {
            // Handle both cases where response might be a full page or API response
            let loan;
            if (response.data) {
                // API response structure
                loan = response.data;
            } else {
                // This shouldn't happen with the show method, but just in case
                swal('Error', 'Invalid response format', 'error');
                return;
            }
            
            let html = `
                <div class="modal fade" id="returnItemModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Return Loan Items</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <strong>Loan No:</strong> ${loan.loan_no || 'N/A'}<br>
                                    <strong>Origin Room:</strong> ${loan.originRoom?.room_name || 'N/A'}<br>
                                    <strong>Target Room:</strong> ${loan.targetRoom?.room_name || 'N/A'}<br>
                                    <strong>PIC:</strong> ${loan.picRoom?.employee_name || 'N/A'}
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Select</th>
                                                <th>Asset Name</th>
                                                <th>Register Code</th>
                                                <th>Expected Return</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="loanItemsList">
            `;

            if (loan.loanItems && loan.loanItems.length > 0) {
                loan.loanItems.forEach(item => {
                    const isChecked = item.returned_at ? 'checked disabled' : '';
                    const status = item.returned_at ? 'Returned' : 'Borrowed';
                    const statusClass = item.returned_at ? 'text-success' : 'text-warning';
                    
                    html += `
                        <tr>
                            <td>
                                <input type="checkbox" class="return-checkbox" value="${item.id}" ${isChecked}
                                    data-asset-name="${item.asset?.name || 'Unknown Asset'}">
                            </td>
                            <td>${item.asset?.name || 'N/A'}</td>
                            <td>${item.asset?.register_code || 'N/A'}</td>
                            <td>${item.formatted_expected_return_at || '-'}</td>
                            <td><span class="${statusClass}">${status}</span></td>
                        </tr>
                    `;
                });
            } else {
                html += `
                    <tr>
                        <td colspan="6">No loan items found for this loan.</td>
                    </tr>
                `;
            }

            html += `
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="form-group mt-3">
                                    <label class="form-label">Return Date</label>
                                    <input type="date" id="returnDate" class="form-control" value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                
                                <div class="form-group mt-3">
                                    <label class="form-label">Returned By (Employee)</label>
                                    <select id="returnEmployee" class="form-control">
                                        <option value="">Select Employee</option>
            `;

            // Add employees to the dropdown via AJAX
            $.get('/dropdown/employee', function(employees) {
                if (employees.data) {
                    employees.data.forEach(emp => {
                        html += `<option value="${emp.id}">${emp.employee_name}</option>`;
                    });
                }
                
                html += `
                                    </select>
                                </div>
                            </div>
                            <div class="modal-footer">
                               <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                               <button type="button" class="btn btn-primary" id="submitReturn">Submit Return</button>
                           </div>
                       </div>
                   </div>
               </div>
           `;

                // Add the HTML to the page and show the modal
                $('body').append(html);
                
                // Initialize select2 for employee selection after modal is added to DOM
                $('#returnEmployee').select2();
                
                // Handle submit return
                $('#submitReturn').click(function() {
                    handleReturnSubmit(loanId);
                });
                
                // Show the modal
                $('#returnItemModal').modal('show');
                
                // Cleanup modal when hidden
                $('#returnItemModal').on('hidden.bs.modal', function() {
                    $(this).remove();
                });
            });
        }).fail(function(xhr) {
            const response = xhr.responseJSON;
            swal('Error', response?.message || 'Failed to load loan details', 'error');
        });
    }

    // Handle return submission
    function handleReturnSubmit(loanId) {
        const selectedItems = $('.return-checkbox:checked').map(function() {
            return this.value;
        }).get();
        
        if (selectedItems.length === 0) {
            swal('Warning', 'Please select at least one item to return', 'warning');
            return;
        }
        
        const returnDate = $('#returnDate').val();
        const returnEmployee = $('#returnEmployee').val();
        
        if (!returnDate) {
            swal('Error', 'Please select return date', 'error');
            return;
        }
        
        if (!returnEmployee) {
            swal('Error', 'Please select employee who handled the return', 'error');
            return;
        }
        
        // Get names of assets being returned for confirmation
        let assetNames = [];
        $('.return-checkbox:checked').each(function() {
            assetNames.push($(this).data('asset-name'));
        });
        
        const confirmationMessage = `Return the following items?\n\n${assetNames.join('\n')}`;
        
        if (confirm(confirmationMessage)) {
            $.ajax({
                url: `/manajemen-aset/asset-loan/${loanId}/return-items`,
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').val(),
                    item_ids: selectedItems,
                    returned_at: returnDate,
                    return_pic_id: returnEmployee
                },
                success: function(response) {
                    if (response.success) {
                        swal('Success', response.message, 'success');
                        $('#returnItemModal').modal('hide');
                        // Reload the DataTable to reflect changes
                        table.ajax.reload();
                    } else {
                        swal('Error', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    swal('Error', response.message || 'An error occurred while returning items', 'error');
                }
            });
        }
    }

    // Add return item endpoint to the AssetLoanController if it doesn't exist
    // This would normally be handled by ensuring the route exists
    // Route::post('/manajemen-aset/asset-loan/{assetLoan}/return-items', [AssetLoanController::class, 'returnItems'])->name('asset-management.asset-loan.return-items');
});
