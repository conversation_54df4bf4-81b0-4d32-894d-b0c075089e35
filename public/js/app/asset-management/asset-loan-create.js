let loanItems = [];
let itemIndex = 0;

$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();

    // Initialize date picker
    $('#loanedAt').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true
    });

    // Add loan item
    $('#addLoanItem').click(function() {
        $('#assetModal').modal('show');
    });

    // Select asset from modal
    $(document).on('click', '.select-asset', function() {
        const assetId = $(this).data('id');
        const assetName = $(this).data('name');
        const registerCode = $(this).data('register-code');
        const merk = $(this).data('merk');
        const type = $(this).data('type');

        // Add to loan items array
        const item = {
            id: itemIndex++,
            asset_id: assetId,
            asset_name: assetName,
            register_code: registerCode,
            merk: merk,
            type: type,
            expected_return_at: ''
        };

        loanItems.push(item);
        renderLoanItems();

        // Close modal
        $('#assetModal').modal('hide');
    });

    // Remove loan item
    $(document).on('click', '.remove-item', function() {
        const itemId = $(this).data('id');
        loanItems = loanItems.filter(item => item.id !== itemId);
        renderLoanItems();
    });

    // Update expected return date
    $(document).on('change', '.expected-return-date', function() {
        const itemId = $(this).data('id');
        const date = $(this).val();
        const item = loanItems.find(item => item.id === itemId);
        if (item) {
            item.expected_return_at = date;
        }
    });

    // Filter assets by room
    $('#modalRoomFilter').change(function() {
        const roomId = $(this).val();
        if (roomId) {
            $.get(window.assetLoanGetAssetsByRoomRoute, { room_id: roomId }, function(data) {
                updateAssetsTable(data);
            });
        } else {
            // Reset to all assets
            updateAssetsTable(window.allAssetsData);
        }
    });

    // Form submission
    $('#assetLoanForm').submit(function(e) {
        e.preventDefault();

        // Prepare form data
        const formData = new FormData(this);

        // Add loan items to form data
        loanItems.forEach((item, index) => {
            formData.append(`loan_items[${index}][asset_id]`, item.asset_id);
            formData.append(`loan_items[${index}][expected_return_at]`, item.expected_return_at);
        });

        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    swal('Success', response.message, 'success');
                    setTimeout(function() {
                        window.location.href = response.redirect;
                    }, 1500);
                } else {
                    swal('Error', response.message, 'error');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                swal('Error', response.message || 'An error occurred', 'error');
            }
        });
    });
});

function renderLoanItems() {
    let html = '';
    loanItems.forEach((item, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>
                    <strong>${item.asset_name}</strong><br>
                    <small>Code: ${item.register_code} | Merk: ${item.merk} | Type: ${item.type}</small>
                </td>
                <td>
                    <input type="date" class="form-control expected-return-date" 
                        data-id="${item.id}" 
                        value="${item.expected_return_at}" 
                        required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-item" 
                        data-id="${item.id}">Remove</button>
                </td>
            </tr>
        `;
    });

    $('#loanItemsContainer').html(html);

    // Initialize date pickers for new items
    $('.expected-return-date').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true
    });
}

function updateAssetsTable(assets) {
    let html = '';
    assets.forEach(asset => {
        html += `
            <tr>
                <td>${asset.name}</td>
                <td>${asset.register_code}</td>
                <td>${asset.merk}</td>
                <td>${asset.type}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary select-asset" 
                        data-id="${asset.id}" 
                        data-name="${asset.name}"
                        data-register-code="${asset.register_code}"
                        data-merk="${asset.merk}"
                        data-type="${asset.type}">Select</button>
                    </td>
                </td>
            </tr>
        `;
    });

    $('#assetsTable tbody').html(html);
}