<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AssetLoanIndexTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_access_asset_loan_index_page(): void
    {
        // Test that the asset loan index page loads without errors
        $response = $this->get(route('asset-management.asset-loan.index'));

        $response->assertStatus(200);
    }
}