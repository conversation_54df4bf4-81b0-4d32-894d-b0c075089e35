<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AssetLoan;
use App\Models\Asset;
use App\Models\Room;
use App\Models\Employee;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AssetLoanTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_access_asset_loan_index_page(): void
    {
        $response = $this->get(route('asset-management.asset-loan.index'));

        $response->assertStatus(200);
    }

    public function test_can_create_asset_loan(): void
    {
        // Create required data
        $room = Room::factory()->create();
        $targetRoom = Room::factory()->create();
        $employee = Employee::factory()->create();
        $asset = Asset::factory()->create(['room_id' => $room->id, 'status' => 'available']);

        $loanData = [
            'origin_room_id' => $room->id,
            'target_room_id' => $targetRoom->id,
            'pic_room_id' => $employee->id,
            'loaned_at' => now()->format('Y-m-d'),
            'reason' => 'Test loan',
            'loan_items' => [
                [
                    'asset_id' => $asset->id,
                    'expected_return_at' => now()->addDays(7)->format('Y-m-d')
                ]
            ]
        ];

        $response = $this->post(route('asset-management.asset-loan.store'), $loanData);

        $response->assertStatus(200);
        $this->assertDatabaseHas('asset_loans', [
            'origin_room_id' => $room->id,
            'target_room_id' => $targetRoom->id,
            'reason' => 'Test loan'
        ]);
        $this->assertDatabaseHas('asset_loan_items', [
            'asset_id' => $asset->id,
            'status' => 'borrowed'
        ]);
    }

    public function test_can_display_asset_loan_list(): void
    {
        $loan = AssetLoan::factory()->create();

        $response = $this->get(route('asset-management.asset-loan.list'));

        $response->assertStatus(200);
    }

    public function test_can_show_asset_loan_detail(): void
    {
        $loan = AssetLoan::factory()->create();

        $response = $this->get(route('asset-management.asset-loan.show', $loan));

        $response->assertStatus(200);
        $response->assertViewIs('asset-management.asset-loan.show');
    }

    public function test_can_edit_asset_loan(): void
    {
        $loan = AssetLoan::factory()->create();

        $response = $this->get(route('asset-management.asset-loan.edit', $loan));

        $response->assertStatus(200);
        $response->assertViewIs('asset-management.asset-loan.edit');
    }
}